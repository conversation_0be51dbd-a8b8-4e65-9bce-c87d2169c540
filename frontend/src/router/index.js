/**
 * Vue Router 配置
 * 定义应用路由和导航守卫
 */

import { createRouter, createWebHistory } from "vue-router";

// 路由组件懒加载
const Login = () => import("@/views/TheLogin.vue");
const Layout = () => import("@/views/TheLayout.vue");
const ContractDetailPage = () => import("@/views/ContractDetailPage.vue");

// 路由配置
const routes = [
  {
    path: "/",
    redirect: "/login",
  },
  {
    path: "/login",
    name: "Login",
    component: Login,
    meta: {
      title: "用户登录",
      requiresAuth: false,
      transition: "fade",
    },
  },
  {
    path: "/dashboard",
    name: "Dashboard",
    component: Layout,
    meta: {
      title: "工作台",
      requiresAuth: true,
      transition: "slide-left",
    },
    children: [
      {
        path: "",
        name: "HomePage",
        component: () => import("@/views/HomePage.vue"),
        meta: {
          title: "首页",
          requiresAuth: true,
        },
      },
      {
        path: "/submit",
        name: "SubmitPage",
        component: () => import("@/views/SubmitPage.vue"),
        meta: {
          title: "提交合同",
          requiresAuth: true,
          roles: ["employee"],
        },
      },
      {
        path: "/my-contracts",
        name: "MyContractsPage",
        component: () => import("@/views/MyContractsPage.vue"),
        meta: {
          title: "我的合同",
          requiresAuth: true,
          roles: ["employee"],
        },
      },
      {
        path: "/contract-management",
        name: "ContractManagePage",
        component: () => import("@/views/ContractManagePage.vue"),
        meta: {
          title: "合同管理",
          requiresAuth: true,
          roles: ["admin", "legal_officer"],
        },
      },
      {
        path: "/review-management",
        name: "ReviewManagePage",
        component: () => import("@/views/ReviewManagePage.vue"),
        meta: {
          title: "审核管理",
          requiresAuth: true,
          roles: ["reviewer", "county_reviewer", "city_reviewer", "admin"],
        },
      },

      {
        path: "/statistics",
        name: "StatisticsPage",
        component: () => import("@/views/StatisticsPage.vue"),
        meta: {
          title: "统计分析",
          requiresAuth: true,
          roles: ["employee", "reviewer", "county_reviewer", "city_reviewer", "admin"],
        },
      },
      {
        path: "/user-management",
        name: "UserManagePage",
        component: () => import("@/views/UserManagePage.vue"),
        meta: {
          title: "用户管理",
          requiresAuth: true,
          roles: ["admin"],
        },
      },
      {
        path: "/system-management",
        name: "SystemManagePage",
        component: () => import("@/views/SystemManagePage.vue"),
        meta: {
          title: "系统管理",
          requiresAuth: true,
          roles: ["admin"],
        },
      },
      {
        path: "/system-stats",
        name: "SystemStatsPage",
        component: () => import("@/views/SystemManagePage.vue"),
        meta: {
          title: "系统统计",
          requiresAuth: true,
          roles: ["admin"],
        },
      },
      {
        path: "/system-logs",
        name: "SystemLogsPage",
        component: () => import("@/views/SystemLogsPage.vue"),
        meta: {
          title: "系统日志",
          requiresAuth: true,
          roles: ["admin"],
        },
      },
      {
        path: "/chunk-upload-management",
        name: "ChunkUploadManagement",
        component: () => import("@/views/ChunkUploadManagement.vue"),
        meta: {
          title: "分片上传管理",
          requiresAuth: true,
          roles: ["admin"],
        },
      },
      {
        path: "/settings",
        name: "SettingsPage",
        component: () => import("@/views/SettingsPage.vue"),
        meta: {
          title: "系统设置",
          requiresAuth: true,
          roles: ["admin"], // 只有管理员可以访问系统设置
        },
      },
      {
        path: "/profile",
        name: "ProfilePage",
        component: () => import("@/views/ProfilePage.vue"),
        meta: {
          title: "个人资料",
          requiresAuth: true,
        },
      },
      {
        path: "test-datetime",
        name: "TestDateTime",
        component: () => import("@/views/TestDateTime.vue"),
        meta: {
          title: "时间处理测试",
          requiresAuth: true,
        },
      },
      // 开发工具路由（仅在开发环境可用）
      ...(import.meta.env.DEV ? [
        {
          path: "/dev/cache-manager",
          name: "CacheManager",
          component: () => import("@/components/dev/CacheManager.vue"),
          meta: {
            title: "缓存管理工具",
            requiresAuth: true,
            roles: ["admin"],
          },
        },
        {
          path: "/dev/cache-test",
          name: "CacheTest",
          component: () => import("@/views/CacheTestPage.vue"),
          meta: {
            title: "缓存测试页面",
            requiresAuth: true,
            roles: ["admin"],
          },
        }
      ] : []),
      {
        path: "/contract/:id",
        name: "ContractDetailPage",
        component: ContractDetailPage,
        meta: {
          title: "合同详情",
          requiresAuth: true,
          roles: ["employee", "reviewer", "county_reviewer", "city_reviewer", "legal_officer", "admin"],
        },
      },
      {
        path: "/contracts",
        name: "ContractsPage",
        redirect: "/contract-management",
        meta: {
          title: "合同管理",
          requiresAuth: true,
          roles: ["admin"],
        },
      },
    ],
  },
  {
    path: "/403",
    name: "Forbidden",
    component: () => import("@/views/Forbidden.vue"),
    meta: {
      title: "权限不足",
      requiresAuth: false,
    },
  },

  {
    path: "/404",
    name: "NotFound",
    component: () => import("@/views/NotFound.vue"),
    meta: {
      title: "页面不存在",
      requiresAuth: false,
    },
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 导入权限管理
import { useUserStore } from "@/stores/user";
import { usePermission } from "@/composables/usePermission";

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 宿迁烟草合同审核系统`;
  } else {
    document.title = "宿迁烟草合同审核系统";
  }

  const userStore = useUserStore();
  const { hasRole, canAccessRoute } = usePermission();

  // 从Pinia store获取认证状态
  const isAuthenticated = userStore.isAuthenticated;
  const userRole = userStore.userRole;

  // 调试日志（仅开发环境）
  if (import.meta.env.DEV) {
    console.log("路由守卫检查:", {
      to: to.path,
      isAuthenticated: isAuthenticated,
      userRole: userRole,
      requiredRoles: to.meta.roles,
      requiresAuth: to.meta.requiresAuth,
    });
  }

  // 公共页面（不需要认证）
  const publicPages = ["/login", "/404", "/403"];
  if (publicPages.includes(to.path)) {
    // 如果用户已登录但访问登录页，重定向到工作台
    if (to.path === "/login" && isAuthenticated) {
      next("/dashboard");
      return;
    }
    next();
    return;
  }

  // 检查认证状态
  if (to.meta.requiresAuth && !isAuthenticated) {
    next({
      path: "/login",
      query: { redirect: to.fullPath },
    });
    return;
  }

  // 检查角色权限
  if (to.meta.roles && isAuthenticated) {
    const hasRequiredRole = hasRole(to.meta.roles);

    if (!hasRequiredRole) {
      // 记录权限检查失败
      console.warn("❌ 用户角色权限不足:", {
        userRole: userRole,
        requiredRoles: to.meta.roles,
        path: to.path,
      });

      // 重定向到403权限不足页面
      next({
        path: "/403",
        query: {
          from: to.fullPath,
          reason: "insufficient_role",
        },
      });
      return;
    }
  }



  next();
});

// 全局后置钩子
router.afterEach((to, from) => {
  // 页面加载完成后的处理

  // 隐藏全局加载状态
  if (window.hideGlobalLoading) {
    setTimeout(() => {
      window.hideGlobalLoading();
    }, 100);
  }
});

// 路由错误处理
router.onError((error) => {
  if (import.meta.env.DEV) {
    console.error("路由错误:", error);
  }

  // 在生产环境中，可以将错误发送到监控服务
  if (import.meta.env.PROD) {
    // sendErrorToMonitoring(error)
  }
});

export default router;
