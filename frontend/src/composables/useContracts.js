/**
 * 合同数据管理 Composable
 * 管理合同相关的数据和操作
 */

import { ref, computed, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { contractsAPI, contractUtils } from "@/api/contracts";
import { useAuth } from "./useAuth";
import pdfCacheService from "@/services/pdfCacheService.js";
// 缓存相关导入已移除，系统直接从API获取数据

// 全局状态 - 确保所有组件实例共享同一个状态
const globalContracts = ref([]);
const globalStats = ref({
  pending: 0,
  approved: 0,
  rejected: 0,
  total: 0,
});
const globalLoading = ref(false);
const globalSubmitting = ref(false);
const globalCurrentContract = ref(null);

/**
 * 清除全局合同状态
 * 用于用户登出或切换时清除缓存数据
 */
export const clearContractsState = () => {
  globalContracts.value = [];
  globalStats.value = {
    pending: 0,
    approved: 0,
    rejected: 0,
    total: 0,
  };
  globalCurrentContract.value = null;
};

/**
 * 合同管理 Hook
 */
export function useContracts() {
  const { user } = useAuth();

  // 使用全局状态
  const loading = globalLoading;
  const submitting = globalSubmitting;
  const contracts = globalContracts;
  const currentContract = globalCurrentContract;
  const stats = globalStats;

  // 分页信息
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
  });

  // 查询条件
  const filters = reactive({
    status: "",
    submitter_id: "",
    reviewer_id: "",
    keyword: "",
  });

  /**
   * 获取合同列表
   * @param {Object} params - 查询参数
   */
  const getContracts = async (params = {}) => {
    try {
      loading.value = true;

      const queryParams = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...filters,
        ...params,
      };

      // 清除空值
      Object.keys(queryParams).forEach((key) => {
        if (
          queryParams[key] === "" ||
          queryParams[key] === null ||
          queryParams[key] === undefined
        ) {
          delete queryParams[key];
        }
      });

      const response = await contractsAPI.getList(queryParams);

      if (response.success) {
        contracts.value = response.data;
        pagination.page = response.pagination.page;
        pagination.pageSize = response.pagination.pageSize;
        pagination.total = response.pagination.total;
        pagination.totalPages = response.pagination.totalPages;

        // PDF缓存预热：预加载前几个合同的PDF文件
        if (response.data && response.data.length > 0) {
          const contractIds = response.data
            .slice(0, 5)
            .map((contract) => contract.id); // 预加载前5个
          pdfCacheService.warmupCache(contractIds).catch((error) => {
            console.warn("PDF缓存预热失败:", error);
          });
        }
      }

      return response;
    } catch (error) {
      // 全局错误处理器会处理API错误
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取合同详情
   * @param {number} id - 合同ID
   */
  const getContractDetail = async (id) => {
    try {
      loading.value = true;

      const response = await contractsAPI.getDetail(id);

      if (response.success) {
        currentContract.value = response.data;
      }

      return response.data;
    } catch (error) {
      // 全局错误处理器会处理API错误
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交合同
   * @param {Object} contractData - 合同数据
   */
  const submitContract = async (contractData) => {
    try {
      submitting.value = true;

      const response = await contractsAPI.submit(contractData);

      if (response.success) {
        ElMessage.success("合同提交成功");

        // 缓存相关代码已移除，系统直接从API获取最新数据

        // 异步刷新数据，不阻塞界面响应
        setTimeout(async () => {
          try {
            await Promise.all([getContracts(), getStats()]);
          } catch (error) {
            if (import.meta.env.DEV) {
              console.error("刷新数据失败:", error);
            }
            // 静默处理刷新失败，不影响用户体验
          }
        }, 100); // 延迟100ms执行，确保界面先响应
      }

      return response.data;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("提交合同失败:", error);
      }
      ElMessage.error(error.message || "提交合同失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 修改合同
   * @param {number} id - 合同ID
   * @param {Object} contractData - 合同数据
   */
  const updateContract = async (id, contractData) => {
    try {
      submitting.value = true;

      const oldContract = currentContract.value;
      const response = await contractsAPI.update(id, contractData);

      if (response.success) {
        ElMessage.success("合同修改成功");

        // 更新当前合同信息
        if (currentContract.value && currentContract.value.id === id) {
          currentContract.value = response.data;
        }

        // 检查是否有状态变更
        const statusChanged =
          oldContract && oldContract.status !== response.data.status;
        const updatedFields = Object.keys(contractData);

        // 缓存相关代码已移除，系统直接从API获取最新数据

        // 异步刷新数据，不阻塞界面响应
        setTimeout(async () => {
          try {
            await Promise.all([getContracts(), getStats()]);
          } catch (error) {
            if (import.meta.env.DEV) {
              console.error("刷新数据失败:", error);
            }
          }
        }, 100);
      }

      return response.data;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("修改合同失败:", error);
      }
      ElMessage.error(error.message || "修改合同失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 删除合同
   * @param {number} id - 合同ID
   */
  const deleteContract = async (id) => {
    try {
      await ElMessageBox.confirm(
        "确定要删除这个合同吗？删除后无法恢复。",
        "确认删除",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        },
      );

      loading.value = true;

      // 获取要删除的合同信息（用于事件和统计更新）
      const contractToDelete = contracts.value.find((c) => c.id === id);

      const response = await contractsAPI.delete(id);

      if (response.success) {
        ElMessage.success("合同删除成功");

        // 缓存相关代码已移除，系统直接从API获取最新数据

        // 立即从本地列表中移除已删除的合同
        const contractIndex = contracts.value.findIndex((c) => c.id === id);
        if (contractIndex !== -1) {
          contracts.value.splice(contractIndex, 1);
        }

        // 更新本地统计数据
        if (contractToDelete) {
          updateStatsAfterDelete(contractToDelete);
        }

        // 异步刷新数据，确保数据一致性
        setTimeout(async () => {
          try {
            await Promise.all([getContracts(), getStats()]);
          } catch (error) {
            if (import.meta.env.DEV) {
              console.error("刷新数据失败:", error);
            }
          }
        }, 100);
      }

      return response;
    } catch (error) {
      if (error !== "cancel") {
        if (import.meta.env.DEV) {
          console.error("删除合同失败:", error);
        }
        ElMessage.error(error.message || "删除合同失败");
      }
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取统计信息
   */
  const getStats = async () => {
    try {
      const response = await contractsAPI.getStats();

      if (response.success) {
        // 转换API返回的数据结构为前端期望的格式
        const apiData = response.data;
        const statusBreakdown = apiData.statusBreakdown || {};

        stats.value = {
          total: apiData.total || 0,
          pending: (statusBreakdown.pending || 0) + (statusBreakdown.pending_city_review || 0) + (statusBreakdown.pending_contract_number || 0),
          approved: statusBreakdown.approved || 0,
          completed: statusBreakdown.completed || 0,
          rejected: statusBreakdown.rejected || 0,
        };
      }

      return response.data;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("获取统计信息失败:", error);
      }
      // 统计信息失败不显示错误消息
    }
  };

  /**
   * 删除合同后更新本地统计数据
   * @param {Object} deletedContract - 被删除的合同对象
   */
  const updateStatsAfterDelete = (deletedContract) => {
    if (!deletedContract || !stats.value) return;

    // 总数减1
    if (stats.value.total > 0) {
      stats.value.total -= 1;
    }

    // 根据合同状态减少对应的计数
    switch (deletedContract.status) {
      case "pending":
        if (stats.value.pending > 0) {
          stats.value.pending -= 1;
        }
        break;
      case "approved":
        if (stats.value.approved > 0) {
          stats.value.approved -= 1;
        }
        break;
      case "rejected":
        if (stats.value.rejected > 0) {
          stats.value.rejected -= 1;
        }
        break;
      default:
        console.warn("未知的合同状态:", deletedContract.status);
    }
  };

  /**
   * 重新提交合同
   * @param {number} id - 合同ID
   * @param {Object} contractData - 合同数据
   */
  const resubmitContract = async (id, contractData) => {
    try {
      submitting.value = true;

      const response = await contractsAPI.resubmit(id, contractData);

      if (response.success) {
        ElMessage.success("合同重新提交成功");
        // 更新当前合同信息
        if (currentContract.value && currentContract.value.id === id) {
          currentContract.value = response.data;
        }
        // 刷新列表
        await getContracts();
        await getStats();
      }

      return response.data;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("重新提交合同失败:", error);
      }
      ElMessage.error(error.message || "重新提交合同失败");
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 设置过滤条件
   * @param {Object} newFilters - 新的过滤条件
   */
  const setFilters = (newFilters) => {
    Object.assign(filters, newFilters);
    pagination.page = 1; // 重置到第一页
  };

  /**
   * 重置过滤条件
   */
  const resetFilters = () => {
    Object.keys(filters).forEach((key) => {
      filters[key] = "";
    });
    pagination.page = 1;
  };

  /**
   * 设置分页
   * @param {number} page - 页码
   * @param {number} pageSize - 每页数量
   */
  const setPagination = (page, pageSize) => {
    pagination.page = page;
    if (pageSize) {
      pagination.pageSize = pageSize;
    }
  };

  /**
   * 提交审核结果
   * @param {number} id - 合同ID
   * @param {Object} reviewData - 审核数据
   */
  const submitReview = async (id, reviewData) => {
    try {
      submitting.value = true;

      const response = await contractsAPI.submitReview(id, reviewData);

      if (response.success) {
        const message =
          reviewData.result === "approved" ? "审核通过" : "审核不通过";
        ElMessage.success(message);
        // 更新当前合同信息
        if (currentContract.value && currentContract.value.id === id) {
          currentContract.value = response.data;
        }
        // 立即刷新数据，确保界面显示最新状态
        try {
          await Promise.all([getContracts(), getStats()]);
        } catch (error) {
          if (import.meta.env.DEV) {
            console.error("刷新数据失败:", error);
          }
        }
      }

      return response.data;
    } catch (error) {
      // 全局错误处理器会处理API错误
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 刷新数据
   */
  const refresh = async () => {
    try {
      await Promise.all([getContracts(), getStats()]);
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("刷新数据失败:", error);
      }
      // 静默处理刷新失败，不影响用户体验
    }
  };

  // 计算属性
  const hasContracts = computed(() => contracts.value.length > 0);
  const isEmpty = computed(
    () => !loading.value && contracts.value.length === 0,
  );

  // 权限检查
  const canModify = (contract) => contractUtils.canModify(contract, user.value);
  const canReview = (contract) => contractUtils.canReview(contract, user.value);
  const canView = (contract) => contractUtils.canView(contract, user.value);

  // 格式化工具
  const formatStatus = contractUtils.formatStatus;
  const getStatusColor = contractUtils.getStatusColor;
  const formatFileSize = contractUtils.formatFileSize;
  const formatDateTime = contractUtils.formatDateTime;
  const getRelativeTime = contractUtils.getRelativeTime;

  return {
    // 状态
    loading: computed(() => loading.value),
    submitting: computed(() => submitting.value),
    contracts: computed(() => contracts.value),
    currentContract: computed(() => currentContract.value),
    stats: computed(() => stats.value),
    pagination: computed(() => pagination),
    filters: computed(() => filters),
    hasContracts,
    isEmpty,

    // 方法
    getContracts,
    getContractDetail,
    submitContract,
    updateContract,
    deleteContract,
    resubmitContract,
    submitReview,
    getStats,
    updateStatsAfterDelete,
    setFilters,
    resetFilters,
    setPagination,
    refresh,

    // 权限检查
    canModify,
    canReview,
    canView,

    // 用户信息
    user,

    // 工具函数
    formatStatus,
    getStatusColor,
    formatFileSize,
    formatDateTime,
    getRelativeTime,
  };
}
